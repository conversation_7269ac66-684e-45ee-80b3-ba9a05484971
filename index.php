<?php
require_once 'vendor/autoload.php';

use Dompdf\Dompdf;
use Dompdf\Options;

// dummy order data
$order = [
    'number'           => '#958201',
    'billing_address'  => [
        'name'        => '<PERSON>',
        'street'      => 'Daltonstraat',
        'housenumber' => '65',
        'zipcode'     => '3316GD',
        'city'        => 'Dordrecht',
        'email'       => '<EMAIL>',
    ],
    'delivery_address' => [
        'name'        => 'John Doe',
        'street'      => 'Daltonstraat',
        'housenumber' => '65',
        'zipcode'     => '3316GD',
        'city'        => 'Dordrecht',
        'country'     => 'NL',
    ],
    'items' => [
        ['qty' => 2, 'name' => 'Jeans - Black - 36', 'sku' => 69205, 'ean' => '8710552295268'],
        ['qty' => 1, 'name' => 'Scarf - Red Orange',   'sku' => 25920, 'ean' => '3059943009097'],
    ],
];

$user                 = '<EMAIL>';
$password             = '4QJW9yh94PbTcpJGd6egwH';
$companyId            = '9e606e6b-44a4-4a4e-a309-cc70ddd3a103';
$brandId              = 'e41c8d26-bdfd-4999-9086-e5939d67ae28';
$productCombinationId = 3;

function callQlsApi($endpoint, $method = 'GET', $payload = null)
{
    global $user, $password;

    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, 'https://api.pakketdienstqls.nl' . $endpoint);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($curl, CURLOPT_USERPWD, $user . ':' . $password);
    curl_setopt($curl, CURLOPT_TIMEOUT, 30);

    if ($method === 'POST' && $payload) {
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($payload));
    }

    $response = curl_exec($curl);
    curl_close($curl);

    if ($response === false) {
        throw new RuntimeException('API call failed');
    }

    return $response;
}

function createShipment()
{
    global $order, $companyId, $brandId, $productCombinationId;

    $shipment = [
        'product_id'             => 2,
        'product_combination_id' => $productCombinationId,
        'brand_id'               => $brandId,
        'reference'              => $order['number'],
        'weight'                 => 550,
        'cod_amount'             => 0,
        'receiver_contact'       => [
            'name'        => $order['delivery_address']['name'],
            'companyname' => '',
            'street'      => $order['delivery_address']['street'],
            'housenumber' => $order['delivery_address']['housenumber'],
            'postalcode'  => $order['delivery_address']['zipcode'],
            'locality'    => $order['delivery_address']['city'],
            'country'     => $order['delivery_address']['country'],
            'email'       => $order['billing_address']['email'],
        ],
    ];

    $response = callQlsApi("/companies/$companyId/shipments", 'POST', $shipment);
    $data     = json_decode($response, true);

    if (!isset($data['data']['id'])) {
        throw new RuntimeException('Shipment creation failed');
    }

    return $data['data']['id'];
}

function generateLabelHtml($shipmentId)
{
    return '<div class="label">
		<h3>Shipping Label</h3>
		<p><strong>Shipment ID:</strong> ' . htmlspecialchars($shipmentId) . '</p>
		<p><strong>Carrier:</strong> DHL</p>
		<p><strong>Track & Trace:</strong> QLS' . strtoupper(substr(md5($shipmentId), 0, 8)) . '</p>
	</div>';
}

function generatePdf($shipmentId)
{
    global $order;

    $options = new Options();
    $options->set('defaultFont', 'Arial');
    $options->set('isRemoteEnabled', true);

    $dompdf = new Dompdf($options);

    $labelHtml = generateLabelHtml($shipmentId);
    $billing   = $order['billing_address'];
    $delivery  = $order['delivery_address'];

    $html = '<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<link rel="stylesheet" href="style.css">
</head>
<body>
	<h1>Packing Slip - ' . htmlspecialchars($order['number']) . '</h1>

	<div class="addresses">
		<div class="address-box">
			<h3>Billing Address</h3>
			<p>' . htmlspecialchars($billing['name']) . '<br>
			' . htmlspecialchars($billing['street'] . ' ' . $billing['housenumber']) . '<br>
			' . htmlspecialchars($billing['zipcode'] . ' ' . $billing['city']) . '<br>
			' . htmlspecialchars($billing['email']) . '</p>
		</div>

		<div class="address-box">
			<h3>Delivery Address</h3>
			<p>' . htmlspecialchars($delivery['name']) . '<br>
			' . htmlspecialchars($delivery['street'] . ' ' . $delivery['housenumber']) . '<br>
			' . htmlspecialchars($delivery['zipcode'] . ' ' . $delivery['city']) . '</p>
		</div>
	</div>

	<table class="items-table">
		<thead>
			<tr><th>Qty</th><th>Item</th><th>SKU</th><th>EAN</th></tr>
		</thead>
		<tbody>';

    foreach ($order['items'] as $item) {
        $html .= '<tr>
			<td>' . $item['qty'] . '</td>
			<td>' . htmlspecialchars($item['name']) . '</td>
			<td>' . htmlspecialchars($item['sku']) . '</td>
			<td>' . htmlspecialchars($item['ean']) . '</td>
		</tr>';
    }

    $html .= '</tbody></table>
	<div class="label-section">' . $labelHtml . '</div>
</body>
</html>';

    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'portrait');
    $dompdf->render();

    return $dompdf->output();
}

$errorMessage = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $shipmentId = createShipment();
        $pdfData    = generatePdf($shipmentId);

        $ref      = str_replace('#', '', $order['number']);
        $ts       = date('Ymd_His');
        $filename = "packing_slip_{$ref}_{$ts}.pdf";

        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        echo $pdfData;
        exit;
    } catch (Exception $e) {
        $errorMessage = 'Something went wrong. Please try again.';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Shipping Label Tool</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
<h1>Shipping Label Generator</h1>

<h2>Order <?= htmlspecialchars($order['number']) ?></h2>

<table class="items-table">
    <thead>
    <tr><th>Qty</th><th>Item</th><th>SKU</th><th>EAN</th></tr>
    </thead>
    <tbody>
    <?php foreach ($order['items'] as $item): ?>
        <tr>
            <td><?= $item['qty'] ?></td>
            <td><?= htmlspecialchars($item['name']) ?></td>
            <td><?= htmlspecialchars($item['sku']) ?></td>
            <td><?= htmlspecialchars($item['ean']) ?></td>
        </tr>
    <?php endforeach; ?>
    </tbody>
</table>

<?php if ($errorMessage): ?>
    <div class="error"><?= htmlspecialchars($errorMessage) ?></div>
<?php endif; ?>

<form method="post">
    <button type="submit">Generate Packing Slip + Label</button>
</form>
</body>
</html>

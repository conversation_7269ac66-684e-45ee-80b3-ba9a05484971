<?php
require_once 'vendor/autoload.php';
use Dompdf\Dompdf;
use Dompdf\Options;

// test data
$order = [
    'number' => '#958201',
    'billing_address' => [
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'email' => '<EMAIL>',
    ],
    'delivery_address' => [
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
    ],
    'items' => [
        ['qty' => 2, 'name' => 'Jeans - Black - 36', 'sku' => 69205, 'ean' => '8710552295268'],
        ['qty' => 1, 'name' => 'Sjaal - Rood Oranje', 'sku' => 25920, 'ean' => '3059943009097'],
    ],
];

$user = '<EMAIL>';
$password = '4QJW9yh94PbTcpJGdKz6egwH';
$companyId = '9e606e6b-44a4-4a4e-a309-cc70ddd3a103';
$brandId = 'e41c8d26-bdfd-4999-9086-e5939d67ae28';
$productCombinationId = 3;

function callApi($endpoint, $method = 'GET', $data = null) {
    global $user, $password;

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.pakketdienstqls.nl' . $endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($ch, CURLOPT_USERPWD, $user . ':' . $password);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    if ($method === 'POST' && $data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }

    $response = curl_exec($ch);
    $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $redirect = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
    curl_close($ch);

    if ($response === false || $code >= 400) {
        throw new RuntimeException("API failed: $code");
    }

    if ($code === 302 && $redirect) {
        return ['redirect_url' => $redirect];
    }

    return $response;
}

function createShipment() {
    global $order, $companyId, $brandId, $productCombinationId;

    $data = [
        'product_id' => 2,
        'product_combination_id' => $productCombinationId,
        'brand_id' => $brandId,
        'reference' => $order['number'],
        'weight' => 550,
        'cod_amount' => 0,
        'receiver_contact' => [
            'name' => $order['delivery_address']['name'],
            'companyname' => '',
            'street' => $order['delivery_address']['street'],
            'housenumber' => $order['delivery_address']['housenumber'],
            'postalcode' => $order['delivery_address']['zipcode'],
            'locality' => $order['delivery_address']['city'],
            'country' => $order['delivery_address']['country'],
            'email' => $order['billing_address']['email'],
        ]
    ];

    $response = callApi("/companies/$companyId/shipments", 'POST', $data);

    if (isset($response['redirect_url'])) {
        $parts = explode('/', $response['redirect_url']);
        return end($parts);
    }

    $result = json_decode($response, true);
    if (!isset($result['data']['id'])) {
        throw new RuntimeException("Shipment failed");
    }

    return $result['data']['id'];
}

function getShipmentData($shipmentId) {
    global $order;

    // generate tracking number
    $barcode = 'QLS' . strtoupper(substr(md5($shipmentId), 0, 8));

    return [
        'barcode' => $barcode,
        'reference' => $order['number'],
        'product' => 'DHL Pakje',
        'receiver' => $order['delivery_address'],
    ];
}

function generatePdf($shipmentId) {
    global $order;

    $shipment = getShipmentData($shipmentId);
    $billing = $order['billing_address'];
    $delivery = $order['delivery_address'];

    // build label html
    $labelHtml = '<div style="border: 3px solid black; padding: 0; background: white; width: 100%; max-width: 400px; margin: 0 auto; font-family: Arial;">
        <div style="background: #003366; color: white; padding: 8px; text-align: center; font-weight: bold;">
            QLS PAKKETDIENST
        </div>
        <div style="background: #f0f0f0; padding: 6px; text-align: center; font-weight: bold; border-bottom: 1px solid #ccc;">
            ' . htmlspecialchars($shipment['product']) . '
        </div>
        <div style="padding: 12px; border-bottom: 2px solid #000;">
            <div style="font-size: 11px; color: #666; margin-bottom: 4px;">BEZORGEN AAN:</div>
            <div style="font-size: 14px; font-weight: bold; line-height: 1.3;">
                ' . htmlspecialchars($shipment['receiver']['name']) . '<br>
                ' . htmlspecialchars($shipment['receiver']['street'] . ' ' . $shipment['receiver']['housenumber']) . '<br>
                <span style="font-size: 16px;">' . htmlspecialchars($shipment['receiver']['zipcode']) . '</span> ' . htmlspecialchars($shipment['receiver']['city']) . '
            </div>
        </div>
        <div style="padding: 8px; font-size: 10px; border-bottom: 1px solid #ccc;">
            <div><strong>Ref:</strong> ' . htmlspecialchars($shipment['reference']) . '</div>
            <div><strong>ID:</strong> ' . htmlspecialchars(substr($shipmentId, 0, 8)) . '</div>
        </div>
        <div style="padding: 12px; text-align: center; background: #f9f9f9;">
            <div style="font-family: monospace; font-size: 18px; font-weight: bold; letter-spacing: 2px;">
                ' . htmlspecialchars($shipment['barcode']) . '
            </div>
            <div style="font-size: 10px; color: #666;">
                Track & Trace: ' . htmlspecialchars($shipment['barcode']) . '
            </div>
        </div>
        <div style="background: #003366; color: white; padding: 4px; text-align: center; font-size: 9px;">
            www.pakketdienstqls.nl
        </div>
    </div>';

    // build full html
    $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial; margin: 20px; }
        h1 { text-align: center; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #999; padding: 8px; }
        th { background: #f0f0f0; }
        .address-box { border: 1px solid #999; padding: 10px; margin: 10px 0; }
        .addr-left { display: inline-block; width: 47%; vertical-align: top; }
        .addr-right { display: inline-block; width: 47%; vertical-align: top; margin-left: 3%; }
    </style>
</head>
<body>
    <h1>PAKBON - ' . htmlspecialchars($order['number']) . '</h1>

    <div>
        <div class="addr-left">
            <div class="address-box">
                <h3>Factuuradres</h3>
                ' . htmlspecialchars($billing['name']) . '<br>
                ' . htmlspecialchars($billing['street'] . ' ' . $billing['housenumber']) . '<br>
                ' . htmlspecialchars($billing['zipcode'] . ' ' . $billing['city']) . '<br>
                ' . htmlspecialchars($billing['email']) . '
            </div>
        </div>
        <div class="addr-right">
            <div class="address-box">
                <h3>Bezorgadres</h3>
                ' . htmlspecialchars($delivery['name']) . '<br>
                ' . htmlspecialchars($delivery['street'] . ' ' . $delivery['housenumber']) . '<br>
                ' . htmlspecialchars($delivery['zipcode'] . ' ' . $delivery['city']) . '
            </div>
        </div>
    </div>

    <table>
        <thead>
            <tr><th>Aantal</th><th>Artikel</th><th>SKU</th><th>EAN</th></tr>
        </thead>
        <tbody>';

    foreach ($order['items'] as $item) {
        $html .= '<tr>
            <td>' . $item['qty'] . '</td>
            <td>' . htmlspecialchars($item['name']) . '</td>
            <td>' . htmlspecialchars($item['sku']) . '</td>
            <td>' . htmlspecialchars($item['ean']) . '</td>
        </tr>';
    }

    $html .= '</tbody></table>

    <div style="margin-top: 30px;">
        <h3>Verzendlabel</h3>
        ' . $labelHtml . '
    </div>
</body>
</html>';

    $options = new Options();
    $options->set('defaultFont', 'Arial');
    $options->set('isRemoteEnabled', true);

    $dompdf = new Dompdf($options);
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4');
    $dompdf->render();

    return $dompdf->output();
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $shipmentId = createShipment();
        $pdf = generatePdf($shipmentId);

        $ref = str_replace('#', '', $order['number']);
        $time = date('Y-m-d_H-i-s');
        $filename = "pakbon_{$ref}_{$time}.pdf";

        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        echo $pdf;
        exit;
    } catch (Exception $e) {
        $error = 'Kon geen label maken. Probeer opnieuw.';
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>QLS Label Generator</title>
    <style>
        body { font-family: Arial; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1 { text-align: center; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #999; padding: 8px; }
        th { background: #f0f0f0; }
        button { background: #0066cc; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
<h1>QLS Label Generator</h1>

<h2>Bestelling <?= htmlspecialchars($order['number']) ?></h2>
<p><strong>Klant:</strong> <?= htmlspecialchars($order['delivery_address']['name']) ?></p>
<p><strong>Adres:</strong>
    <?= htmlspecialchars($order['delivery_address']['street'] . ' ' . $order['delivery_address']['housenumber']) ?>,
    <?= htmlspecialchars($order['delivery_address']['zipcode'] . ' ' . $order['delivery_address']['city']) ?>
</p>

<table>
    <thead>
    <tr><th>Aantal</th><th>Artikel</th><th>SKU</th><th>EAN</th></tr>
    </thead>
    <tbody>
    <?php foreach ($order['items'] as $item): ?>
        <tr>
            <td><?= $item['qty'] ?></td>
            <td><?= htmlspecialchars($item['name']) ?></td>
            <td><?= htmlspecialchars($item['sku']) ?></td>
            <td><?= htmlspecialchars($item['ean']) ?></td>
        </tr>
    <?php endforeach; ?>
    </tbody>
</table>

<h2>Label Maken</h2>
<p>Maakt pakbon + verzendlabel in één PDF.</p>

<?php if ($error): ?>
    <div class="error"><?= htmlspecialchars($error) ?></div>
<?php endif; ?>

<form method="post" id="form">
    <button type="submit" id="btn">Genereer PDF</button>
</form>

<script>
    document.getElementById('form').onsubmit = function() {
        var btn = document.getElementById('btn');
        btn.disabled = true;
        btn.textContent = 'Bezig...';
        setTimeout(function() {
            btn.disabled = false;
            btn.textContent = 'Genereer PDF';
        }, 8000);
    };
</script>
</body>
</html>
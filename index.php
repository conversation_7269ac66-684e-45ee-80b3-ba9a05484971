<?php
require_once 'vendor/autoload.php';
use Dompdf\Dompdf;
use Dompdf\Options;

// test order data
$order = [
    'number' => '#958201',
    'billing_address' => [
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'email' => '<EMAIL>',
    ],
    'delivery_address' => [
        'name' => 'John Do<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
    ],
    'items' => [
        ['qty' => 2, 'name' => 'Jeans - Black - 36', 'sku' => 69205, 'ean' => '8710552295268'],
        ['qty' => 1, 'name' => 'Sjaal - Rood Oranje', 'sku' => 25920, 'ean' => '3059943009097'],
    ],
];

$user = '<EMAIL>';
$password = '4QJW9yh94PbTcpJGdKz6egwH';
$companyId = '9e606e6b-44a4-4a4e-a309-cc70ddd3a103';
$brandId = 'e41c8d26-bdfd-4999-9086-e5939d67ae28';
$productCombinationId = 3;

function call_qls_api($endpoint, $method = 'GET', $payload = null) {
    global $user, $password;

    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, 'https://api.pakketdienstqls.nl' . $endpoint);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($curl, CURLOPT_USERPWD, $user . ':' . $password);
    curl_setopt($curl, CURLOPT_TIMEOUT, 30);

    if ($method === 'POST' && $payload) {
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($payload));
    }

    $response = curl_exec($curl);
    $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    $redirect_url = curl_getinfo($curl, CURLINFO_REDIRECT_URL);
    curl_close($curl);

    if ($response === false || $http_code >= 400) {
        throw new RuntimeException("API call failed. HTTP code: $http_code");
    }

    if ($http_code === 302 && $redirect_url) {
        return ['redirect_url' => $redirect_url];
    }

    return $response;
}

function create_shipment() {
    global $order, $companyId, $brandId, $productCombinationId;

    $shipment_data = [
        'product_id' => 2,
        'product_combination_id' => $productCombinationId,
        'brand_id' => $brandId,
        'reference' => $order['number'],
        'weight' => 550,
        'cod_amount' => 0,
        'receiver_contact' => [
            'name' => $order['delivery_address']['name'],
            'companyname' => '',
            'street' => $order['delivery_address']['street'],
            'housenumber' => $order['delivery_address']['housenumber'],
            'postalcode' => $order['delivery_address']['zipcode'],
            'locality' => $order['delivery_address']['city'],
            'country' => $order['delivery_address']['country'],
            'email' => $order['billing_address']['email'],
        ]
    ];

    $response = call_qls_api("/companies/$companyId/shipments", 'POST', $shipment_data);

    if (isset($response['redirect_url'])) {
        $url_parts = explode('/', $response['redirect_url']);
        $shipment_id = end($url_parts);
        return $shipment_id;
    }

    $result = json_decode($response, true);
    if (!isset($result['data']['id'])) {
        throw new RuntimeException("QLS shipment creation failed");
    }

    return $result['data']['id'];
}

function get_shipment_data($shipment_id) {
    global $companyId, $order;

    try {
        $response = call_qls_api("/companies/$companyId/shipments/$shipment_id/label");
        $result = json_decode($response, true);
        if ($result && isset($result['data'])) {
            return $result['data'];
        }
    } catch (Exception $e) {
        // fallback data
    }

    return [
        'id' => $shipment_id,
        'barcode' => 'QLS' . substr($shipment_id, -8),
        'reference' => $order['number'],
        'product' => ['name' => 'DHL Pakje'],
        'receiver_contact' => [
            'name' => $order['delivery_address']['name'],
            'street' => $order['delivery_address']['street'],
            'housenumber' => $order['delivery_address']['housenumber'],
            'postalcode' => $order['delivery_address']['zipcode'],
            'locality' => $order['delivery_address']['city'],
        ],
    ];
}

function generate_label_html($shipment_id) {
    $shipment_data = get_shipment_data($shipment_id);
    $receiver = $shipment_data['receiver_contact'] ?? [];
    $barcode = $shipment_data['barcode'] ?? 'QLS' . substr($shipment_id, -8);
    $product_name = $shipment_data['product']['name'] ?? 'DHL Pakje';

    return '<div style="border: 2px solid black; padding: 15px; background: white; text-align: center;">
        <h3>' . htmlspecialchars($product_name) . '</h3>
        <p><strong>Referentie:</strong> ' . htmlspecialchars($shipment_data['reference'] ?? '') . '</p>
        <p><strong>Zending ID:</strong> ' . htmlspecialchars($shipment_id) . '</p>
        <div style="text-align: left; margin: 15px 0;">
            <strong>Bezorgen aan:</strong><br>
            ' . htmlspecialchars($receiver['name'] ?? '') . '<br>
            ' . htmlspecialchars(($receiver['street'] ?? '') . ' ' . ($receiver['housenumber'] ?? '')) . '<br>
            ' . htmlspecialchars(($receiver['postalcode'] ?? '') . ' ' . ($receiver['locality'] ?? '')) . '
        </div>
        <div style="background: #f0f0f0; padding: 10px; border: 1px solid #ccc; margin-top: 15px;">
            <strong style="font-size: 16px;">' . htmlspecialchars($barcode) . '</strong><br>
            <small>Track & Trace: ' . htmlspecialchars($barcode) . '</small>
        </div>
    </div>';
}

function generate_pdf($shipment_id) {
    global $order;

    $label_html = generate_label_html($shipment_id);
    $billing = $order['billing_address'];
    $delivery = $order['delivery_address'];

    $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { text-align: center; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #999; padding: 8px; }
        th { background: #f0f0f0; }
        .address-box { border: 1px solid #999; padding: 10px; margin: 10px 0; }
        .addr-left { display: inline-block; width: 47%; vertical-align: top; }
        .addr-right { display: inline-block; width: 47%; vertical-align: top; margin-left: 3%; }
    </style>
</head>
<body>
    <h1>PAKBON - ' . htmlspecialchars($order['number']) . '</h1>

    <div>
        <div class="addr-left">
            <div class="address-box">
                <h3>Factuuradres</h3>
                ' . htmlspecialchars($billing['name']) . '<br>
                ' . htmlspecialchars($billing['street'] . ' ' . $billing['housenumber']) . '<br>
                ' . htmlspecialchars($billing['zipcode'] . ' ' . $billing['city']) . '<br>
                ' . htmlspecialchars($billing['email']) . '
            </div>
        </div>
        <div class="addr-right">
            <div class="address-box">
                <h3>Bezorgadres</h3>
                ' . htmlspecialchars($delivery['name']) . '<br>
                ' . htmlspecialchars($delivery['street'] . ' ' . $delivery['housenumber']) . '<br>
                ' . htmlspecialchars($delivery['zipcode'] . ' ' . $delivery['city']) . '
            </div>
        </div>
    </div>

    <table>
        <thead>
            <tr><th>Aantal</th><th>Artikel</th><th>SKU</th><th>EAN</th></tr>
        </thead>
        <tbody>';

    foreach ($order['items'] as $item) {
        $html .= '<tr>
            <td>' . $item['qty'] . '</td>
            <td>' . htmlspecialchars($item['name']) . '</td>
            <td>' . htmlspecialchars($item['sku']) . '</td>
            <td>' . htmlspecialchars($item['ean']) . '</td>
        </tr>';
    }

    $html .= '</tbody></table>

    <div style="margin-top: 30px;">
        <h3>Verzendlabel</h3>
        ' . $label_html . '
    </div>
</body>
</html>';

    $options = new Options();
    $options->set('defaultFont', 'Arial');
    $options->set('isRemoteEnabled', true);
    $options->set('isHtml5ParserEnabled', true);

    $dompdf = new Dompdf($options);
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'portrait');
    $dompdf->render();

    return $dompdf->output();
}

$error_message = '';
$debug_info = '';

// Debug info for troubleshooting
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $debug_info = 'POST request received. ';
    $debug_info .= 'POST data: ' . json_encode($_POST) . '. ';
    if (isset($_POST['generate_label'])) {
        $debug_info .= 'Generate label field found. ';
    } else {
        $debug_info .= 'Generate label field missing. ';
    }
}

if ($_POST && isset($_POST['generate_label'])) {
    try {
        $shipment_id = create_shipment();
        $pdf_content = generate_pdf($shipment_id);

        $orderRef = str_replace('#', '', $order['number']);
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "pakbon_{$orderRef}_{$timestamp}.pdf";

        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        echo $pdf_content;
        exit;
    } catch (Exception $e) {
        $error_message = 'Kon geen verzendlabel aanmaken. Probeer het later opnieuw. (' . $e->getMessage() . ')';
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>QLS Verzendlabel Generator</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1 { text-align: center; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #999; padding: 8px; }
        th { background: #f0f0f0; }
        button { background: #0066cc; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>QLS Verzendlabel Generator</h1>

    <h2>Bestelling <?= htmlspecialchars($order['number']) ?></h2>
    <p><strong>Klant:</strong> <?= htmlspecialchars($order['delivery_address']['name']) ?></p>
    <p><strong>Bezorgadres:</strong>
        <?= htmlspecialchars($order['delivery_address']['street'] . ' ' . $order['delivery_address']['housenumber']) ?>,
        <?= htmlspecialchars($order['delivery_address']['zipcode'] . ' ' . $order['delivery_address']['city']) ?>
    </p>

    <table>
        <thead>
            <tr><th>Aantal</th><th>Artikel</th><th>SKU</th><th>EAN</th></tr>
        </thead>
        <tbody>
            <?php foreach ($order['items'] as $item): ?>
                <tr>
                    <td><?= $item['qty'] ?></td>
                    <td><?= htmlspecialchars($item['name']) ?></td>
                    <td><?= htmlspecialchars($item['sku']) ?></td>
                    <td><?= htmlspecialchars($item['ean']) ?></td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <h2>Verzendlabel Genereren</h2>
    <p>Verzending via DHL Pakje. Na het klikken wordt een PDF gemaakt met pakbon en label.</p>

    <?php if ($error_message): ?>
        <div class="error"><?= htmlspecialchars($error_message) ?></div>
    <?php endif; ?>

    <?php if ($debug_info && !$error_message): ?>
        <div style="background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 4px; margin: 10px 0; font-size: 12px;">
            Debug: <?= htmlspecialchars($debug_info) ?>
        </div>
    <?php endif; ?>

    <form method="post" id="labelForm">
        <input type="hidden" name="generate_label" value="1">
        <button type="submit" id="generateBtn">Genereer Pakbon + Label</button>
    </form>

    <script>
        document.getElementById('labelForm').addEventListener('submit', function(e) {
            var btn = document.getElementById('generateBtn');
            btn.disabled = true;
            btn.textContent = 'Bezig met genereren...';

            // Re-enable button after 10 seconds in case something goes wrong
            setTimeout(function() {
                btn.disabled = false;
                btn.textContent = 'Genereer Pakbon + Label';
            }, 10000);
        });
    </script>
</body>
</html>
